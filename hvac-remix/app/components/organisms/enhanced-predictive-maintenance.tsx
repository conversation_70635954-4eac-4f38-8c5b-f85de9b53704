import React from 'react';
import { useFetcher } from '@remix-run/react';
import { useProgressiveEnhancement } from '~/hooks/use-progressive-enhancement';
import { ServiceType } from '~/contexts/service-availability';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '~/components/ui/alert';
import { Badge } from '~/components/ui/badge';
import { Skeleton } from '~/components/ui/skeleton';
import { InfoIcon, AlertCircle, CheckCircle, AlertTriangle } from 'lucide-react';
import { ServiceAvailabilityIndicator } from '~/components/atoms/service-availability-indicator';

interface EnhancedPredictiveMaintenanceProps {
  equipmentId: string;
  className?: string;
}

/**
 * Enhanced Predictive Maintenance component with progressive enhancement
 *
 * This component shows predictive maintenance data with different levels of functionality
 * based on the availability of the Bielik LLM service:
 * - Full functionality when Bielik LLM is available
 * - Basic functionality with historical data when Bielik LLM is unavailable
 */
export function EnhancedPredictiveMaintenance({
  equipmentId,
  className = '',
}: EnhancedPredictiveMaintenanceProps) {
  const fetcher = useFetcher();
  const { isAvailable } = useProgressiveEnhancement({
    requiredServices: [ServiceType.PREDICTIVE_MAINTENANCE],
  });

  // Fetch data on mount and when equipment ID changes
  React.useEffect(() => {
    if (equipmentId) {
      fetcher.load(`/api/predictive-maintenance?equipmentId=${equipmentId}`);
    }
  }, [equipmentId]);

  // Loading state
  if (fetcher.state === 'loading' || !fetcher.data) {
    return <PredictiveMaintenanceSkeleton />;
  }

  // Error state
  if (fetcher.data && typeof fetcher.data === 'object' && 'success' in fetcher.data && (!fetcher.data.success || fetcher.data.error)) {
    return (
      <Alert variant="destructive" className={className}>
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          {(fetcher.data as any).error || 'Failed to load predictive maintenance data'}
        </AlertDescription>
      </Alert>
    );
  }

  const data = fetcher.data && typeof fetcher.data === 'object' && 'data' in fetcher.data ? (fetcher.data as any).data : null;
  const limitedFunctionality = fetcher.data && typeof fetcher.data === 'object' && 'limitedFunctionality' in fetcher.data ? (fetcher.data as any).limitedFunctionality : false;
  const message = fetcher.data && typeof fetcher.data === 'object' && 'message' in fetcher.data ? (fetcher.data as any).message : null;

  return (
    <Card className={className}>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle>Predictive Maintenance</CardTitle>
            <CardDescription>
              Equipment health and maintenance predictions
            </CardDescription>
          </div>
          <ServiceAvailabilityIndicator serviceType={ServiceType.PREDICTIVE_MAINTENANCE} />
        </div>
      </CardHeader>

      <CardContent>
        {limitedFunctionality && (
          <Alert className="mb-4 bg-amber-50 border-amber-200 dark:bg-amber-900/20 dark:border-amber-900">
            <InfoIcon className="h-4 w-4 text-amber-600 dark:text-amber-400" />
            <AlertDescription className="text-amber-800 dark:text-amber-300">
              {message || 'Limited functionality mode. AI-enhanced predictions unavailable.'}
            </AlertDescription>
          </Alert>
        )}

        <div className="space-y-6">
          {/* Health Score */}
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Health Score</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">
                  {data.healthScore}%
                </div>
                <HealthScoreIndicator score={data.healthScore} />
                <p className="text-xs text-muted-foreground mt-2">
                  {data.healthScore > 70 ? 'Good condition' :
                   data.healthScore > 40 ? 'Needs attention' : 'Requires maintenance'}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Status</CardTitle>
              </CardHeader>
              <CardContent>
                <StatusBadge status={data.status} />
                <p className="text-sm mt-2">
                  {data.status === 'optimal' ? 'Equipment is operating normally' :
                   data.status === 'warning' ? 'Equipment needs attention' :
                   data.status === 'critical' ? 'Immediate maintenance required' :
                   'Scheduled maintenance needed'}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Next Maintenance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-xl font-medium">
                  {data.nextMaintenance ? new Date(data.nextMaintenance).toLocaleDateString() : 'Not scheduled'}
                </div>
                <p className="text-sm text-muted-foreground mt-2">
                  {data.lastMaintenanceDate ?
                    `Last maintenance: ${new Date(data.lastMaintenanceDate).toLocaleDateString()}` :
                    'No previous maintenance recorded'}
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Issues */}
          {data.issues.length > 0 && (
            <div>
              <h3 className="text-lg font-medium mb-3">Detected Issues</h3>
              <div className="space-y-3">
                {data.issues.map((issue: any) => (
                  <Alert
                    key={issue.id}
                    variant={issue.severity === 'high' ? 'destructive' : 'default'}
                    className={
                      issue.severity === 'high' ? 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-900' :
                      issue.severity === 'medium' ? 'bg-amber-50 border-amber-200 dark:bg-amber-900/20 dark:border-amber-900' :
                      'bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-900'
                    }
                  >
                    <div className="flex items-start">
                      <div className="flex-shrink-0 mt-0.5">
                        {issue.severity === 'high' ? (
                          <AlertCircle className="h-4 w-4 text-red-600 dark:text-red-400" />
                        ) : issue.severity === 'medium' ? (
                          <AlertTriangle className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                        ) : (
                          <InfoIcon className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                        )}
                      </div>
                      <div className="ml-3">
                        <h4 className="text-sm font-medium">
                          {issue.component}: {issue.description}
                        </h4>
                        <div className="mt-1 text-sm">
                          <p>{issue.recommendedAction}</p>
                          {issue.estimatedRepairTime && (
                            <p className="text-xs mt-1 font-medium">{issue.estimatedRepairTime}</p>
                          )}
                        </div>
                      </div>
                    </div>
                  </Alert>
                ))}
              </div>
            </div>
          )}

          {/* Last Updated */}
          <div className="text-xs text-muted-foreground text-right">
            Last updated: {new Date(data.lastUpdated).toLocaleString()}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Helper components

function HealthScoreIndicator({ score }: { score: number }) {
  const width = `${score}%`;
  const bgColor = score > 70 ? 'bg-green-500' : score > 40 ? 'bg-amber-500' : 'bg-red-500';

  return (
    <div className="w-full h-2 bg-gray-200 rounded-full mt-2 dark:bg-gray-700">
      <div
        className={`h-2 rounded-full ${bgColor}`}
        style={{ width }}
      />
    </div>
  );
}

function StatusBadge({ status }: { status: string }) {
  switch (status) {
    case 'optimal':
      return (
        <Badge className="bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300 dark:border-green-900/50">
          <CheckCircle className="h-3 w-3 mr-1" /> Optimal
        </Badge>
      );
    case 'warning':
      return (
        <Badge className="bg-amber-100 text-amber-800 border-amber-200 dark:bg-amber-900/30 dark:text-amber-300 dark:border-amber-900/50">
          <AlertTriangle className="h-3 w-3 mr-1" /> Warning
        </Badge>
      );
    case 'critical':
      return (
        <Badge className="bg-red-100 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-300 dark:border-red-900/50">
          <AlertCircle className="h-3 w-3 mr-1" /> Critical
        </Badge>
      );
    case 'maintenance_required':
      return (
        <Badge className="bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-900/50">
          <InfoIcon className="h-3 w-3 mr-1" /> Maintenance Required
        </Badge>
      );
    default:
      return (
        <Badge variant="outline">
          Unknown
        </Badge>
      );
  }
}

function PredictiveMaintenanceSkeleton() {
  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div>
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-4 w-64 mt-2" />
          </div>
          <Skeleton className="h-8 w-8 rounded-full" />
        </div>
      </CardHeader>

      <CardContent>
        <div className="space-y-6">
          <div className="grid gap-4 md:grid-cols-3">
            {[...Array(3)].map((_, i) => (
              <Card key={i}>
                <CardHeader className="pb-2">
                  <Skeleton className="h-4 w-24" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-8 w-16" />
                  <Skeleton className="h-2 w-full mt-2" />
                  <Skeleton className="h-4 w-32 mt-2" />
                </CardContent>
              </Card>
            ))}
          </div>

          <div>
            <Skeleton className="h-6 w-36 mb-3" />
            <div className="space-y-3">
              {[...Array(2)].map((_, i) => (
                <Skeleton key={i} className="h-24 w-full" />
              ))}
            </div>
          </div>

          <div className="flex justify-end">
            <Skeleton className="h-4 w-48" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
