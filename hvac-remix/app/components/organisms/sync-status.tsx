import { useState, useEffect } from 'react';
import { SyncType, SyncStatus as SyncStatusEnum, type SyncStatusUpdate } from '~/utils/sync-manager';
import { Card } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Progress } from '~/components/ui/progress';
import { Alert, AlertDescription, AlertTitle } from '~/components/ui/alert';
import {
  AlertCircle,
  Check,
  RefreshCw,
  WifiOff,
  FileText,
  Users,
  Wrench,
  AlertTriangle
} from 'lucide-react';
import {
  subscribeToOfflineSync,
  getOfflineSyncState,
  initOfflineSync,
  synchronizeAllData
} from '~/services/offlineSync.client';

interface SyncStatusProps {
  onComplete?: () => void;
}

/**
 * Component for displaying and managing data synchronization
 */
export function SyncStatus({ onComplete }: SyncStatusProps) {
  const [state, setState] = useState(getOfflineSyncState());
  const [syncing, setSyncing] = useState(false);
  const [statusUpdates, setStatusUpdates] = useState<Record<SyncType, SyncStatusUpdate | null>>({
    [SyncType.SERVICE_ORDER]: null,
    [SyncType.SERVICE_REPORT]: null,
    [SyncType.CUSTOMER]: null,
    [SyncType.DEVICE]: null,
    [SyncType.IMAGE]: null,
    [SyncType.FILE]: null,
    [SyncType.TECHNICIAN_SCHEDULE]: null,
    [SyncType.REFERENCE_DATA]: null,
    [SyncType.USER_DATA]: null
  });

  // Initialize offline sync
  useEffect(() => {
    // Only run on client
    if (typeof window === 'undefined') return;

    // Initialize offline sync
    initOfflineSync().catch(error => {
      console.error('Error initializing offline sync:', error);
    });

    // Subscribe to state changes
    const unsubscribe = subscribeToOfflineSync(newState => {
      setState(newState);
    });

    return () => {
      unsubscribe();
    };
  }, []);

  // Start sync
  const handleSync = async () => {
    if (!state.isOnline || syncing) return;

    try {
      setSyncing(true);

      // Reset status updates
      setStatusUpdates({
        [SyncType.SERVICE_ORDER]: null,
        [SyncType.SERVICE_REPORT]: null,
        [SyncType.CUSTOMER]: null,
        [SyncType.DEVICE]: null,
        [SyncType.IMAGE]: null,
        [SyncType.FILE]: null,
        [SyncType.TECHNICIAN_SCHEDULE]: null,
        [SyncType.REFERENCE_DATA]: null,
        [SyncType.USER_DATA]: null
      });

      // Start sync with status updates
      const success = await synchronizeAllData((update) => {
        setStatusUpdates(prev => ({
          ...prev,
          [update.type]: update
        }));
      });

      if (success) {
        onComplete?.();
      }
    } catch (error) {
      console.error('Error syncing data:', error);
    } finally {
      setSyncing(false);
    }
  };

  // Get icon for sync type
  const getIconForType = (type: SyncType) => {
    switch (type) {
      case SyncType.SERVICE_ORDER:
      case SyncType.SERVICE_REPORT:
        return <FileText className="h-5 w-5" />;
      case SyncType.CUSTOMER:
        return <Users className="h-5 w-5" />;
      case SyncType.DEVICE:
        return <Wrench className="h-5 w-5" />;
      default:
        return <RefreshCw className="h-5 w-5" />;
    }
  };

  // Get color for sync status
  const getColorForStatus = (status: SyncStatusEnum | null) => {
    if (!status) return 'bg-gray-200 dark:bg-gray-700';

    switch (status) {
      case SyncStatusEnum.COMPLETED:
        return 'bg-green-500';
      case SyncStatusEnum.FAILED:
        return 'bg-red-500';
      case SyncStatusEnum.CONFLICT:
        return 'bg-amber-500';
      case SyncStatusEnum.PARTIAL:
        return 'bg-blue-500';
      case SyncStatusEnum.IN_PROGRESS:
        return 'bg-blue-500';
      default:
        return 'bg-gray-200 dark:bg-gray-700';
    }
  };

  // Calculate overall progress
  const calculateOverallProgress = () => {
    const updates = Object.values(statusUpdates).filter(Boolean) as SyncStatusUpdate[];

    if (updates.length === 0) return 0;

    const totalItems = updates.reduce((sum, update) => sum + update.total, 0);
    const processedItems = updates.reduce((sum, update) => sum + update.processed, 0);

    return totalItems > 0 ? Math.round((processedItems / totalItems) * 100) : 0;
  };

  // Check if sync is complete
  const isSyncComplete = () => {
    const updates = Object.values(statusUpdates).filter(Boolean) as SyncStatusUpdate[];

    if (updates.length === 0) return false;

    return updates.every(update =>
      update.status === SyncStatusEnum.COMPLETED ||
      update.status === SyncStatusEnum.PARTIAL ||
      update.status === SyncStatusEnum.CONFLICT
    );
  };

  // Check if there are conflicts
  const hasConflicts = () => {
    return Object.values(statusUpdates).some(update =>
      update?.status === SyncStatusEnum.CONFLICT ||
      (update?.conflicts && update.conflicts > 0)
    );
  };

  return (
    <div className="space-y-6">
      {!state.isOnline && (
        <Alert variant="destructive">
          <WifiOff className="h-4 w-4" />
          <AlertTitle>Offline</AlertTitle>
          <AlertDescription>
            You are currently offline. Synchronization will be available when you reconnect.
          </AlertDescription>
        </Alert>
      )}

      {hasConflicts() && (
        <Alert className="bg-amber-50 border-amber-200 dark:bg-amber-900/20 dark:border-amber-900">
          <AlertTriangle className="h-4 w-4 text-amber-600 dark:text-amber-400" />
          <AlertTitle>Conflicts Detected</AlertTitle>
          <AlertDescription>
            Some data conflicts were detected during synchronization.
            Please go to the conflict resolution page to resolve them.
          </AlertDescription>
        </Alert>
      )}

      {isSyncComplete() && !hasConflicts() && (
        <Alert className="bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-900">
          <Check className="h-4 w-4 text-green-600 dark:text-green-400" />
          <AlertTitle>Sync Complete</AlertTitle>
          <AlertDescription>
            All data has been successfully synchronized with the server.
          </AlertDescription>
        </Alert>
      )}

      <Card className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold">Data Synchronization</h2>

          <Button
            onClick={handleSync}
            disabled={syncing || !state.isOnline}
            className="min-w-[120px]"
          >
            {syncing ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Syncing...
              </>
            ) : (
              'Sync Now'
            )}
          </Button>
        </div>

        <div className="mb-6">
          <div className="flex justify-between mb-2">
            <span className="text-sm font-medium">Overall Progress</span>
            <span className="text-sm">{calculateOverallProgress()}%</span>
          </div>
          <Progress value={calculateOverallProgress()} className="h-2" />
        </div>

        <div className="space-y-4">
          {Object.entries(statusUpdates)
            .filter(([type]) => [
              SyncType.SERVICE_ORDER,
              SyncType.SERVICE_REPORT,
              SyncType.CUSTOMER,
              SyncType.DEVICE
            ].includes(type as SyncType))
            .map(([type, update]) => (
              <div key={type} className="flex items-center">
                <div className="flex-shrink-0 mr-4">
                  {getIconForType(type as SyncType)}
                </div>

                <div className="flex-grow">
                  <div className="flex justify-between mb-1">
                    <span className="text-sm font-medium">
                      {type === SyncType.SERVICE_ORDER ? 'Service Orders' :
                       type === SyncType.SERVICE_REPORT ? 'Service Reports' :
                       type === SyncType.CUSTOMER ? 'Customers' :
                       type === SyncType.DEVICE ? 'Devices' : type}
                    </span>

                    {update && (
                      <span className="text-xs">
                        {update.processed}/{update.total}
                        {update.conflicts ? ` (${update.conflicts} conflicts)` : ''}
                      </span>
                    )}
                  </div>

                  <div className="h-2 bg-gray-100 dark:bg-gray-800 rounded-full overflow-hidden">
                    <div
                      className={`h-full ${getColorForStatus(update?.status)}`}
                      style={{
                        width: `${update && update.total > 0
                          ? Math.round((update.processed / update.total) * 100)
                          : 0}%`
                      }}
                    />
                  </div>
                </div>

                {update?.status === SyncStatusEnum.IN_PROGRESS && (
                  <div className="flex-shrink-0 ml-4">
                    <RefreshCw className="h-4 w-4 animate-spin text-blue-500" />
                  </div>
                )}

                {update?.status === SyncStatusEnum.COMPLETED && (
                  <div className="flex-shrink-0 ml-4">
                    <Check className="h-4 w-4 text-green-500" />
                  </div>
                )}

                {update?.status === SyncStatusEnum.FAILED && (
                  <div className="flex-shrink-0 ml-4">
                    <AlertCircle className="h-4 w-4 text-red-500" />
                  </div>
                )}

                {update?.status === SyncStatusEnum.CONFLICT && (
                  <div className="flex-shrink-0 ml-4">
                    <AlertTriangle className="h-4 w-4 text-amber-500" />
                  </div>
                )}
              </div>
            ))}
        </div>
      </Card>
    </div>
  );
}
