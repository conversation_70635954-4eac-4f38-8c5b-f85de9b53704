import { json, redirect, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { Form, Link, useActionData, useLoaderData, useNavigation, useSearchParams } from "@remix-run/react";
import { getUser } from "~/session.server";
import { prisma } from "~/db.server";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Select } from "~/components/ui/select";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const user = await getUser(request);

  if (!user || user.role !== "CUSTOMER") {
    throw new Response("Unauthorized", { status: 401 });
  }

  if (!user.customerId) {
    throw new Response("User is not associated with a customer", { status: 400 });
  }

  // Get customer data with devices
  const customer = await prisma.customer.findUnique({
    where: { id: user.customerId },
    include: {
      devices: true,
    },
  });

  if (!customer) {
    throw new Response("Customer not found", { status: 404 });
  }

  return json({ customer });
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const user = await getUser(request);

  if (!user || user.role !== "CUSTOMER") {
    throw new Response("Unauthorized", { status: 401 });
  }

  if (!user.customerId) {
    throw new Response("User is not associated with a customer", { status: 400 });
  }

  const formData = await request.formData();
  const title = formData.get("title") as string;
  const description = formData.get("description") as string;
  const deviceId = formData.get("deviceId") as string;
  const priority = formData.get("priority") as string;
  const preferredDate = formData.get("preferredDate") as string;

  // Validate form data
  const errors: Record<string, string> = {};

  if (!title) {
    errors.title = "Title is required";
  }

  if (!description) {
    errors.description = "Description is required";
  }

  if (!deviceId) {
    errors.deviceId = "Device is required";
  }

  if (!priority) {
    errors.priority = "Priority is required";
  }

  if (Object.keys(errors).length > 0) {
    return json({ errors, success: false });
  }

  try {
    // Create service order
    const serviceOrder = await prisma.serviceOrder.create({
      data: {
        title,
        description,
        priority,
        status: "PENDING",
        scheduledDate: preferredDate ? new Date(preferredDate) : undefined,
        customer: {
          connect: { id: user.customerId },
        },
        device: deviceId ? {
          connect: { id: deviceId },
        } : undefined,
        user: {
          connect: { id: user.id },
        },
      },
    });

    return redirect(`/customer-portal/service-orders/${serviceOrder.id}`);
  } catch (error) {
    console.error("Failed to create service order:", error);
    return json({
      errors: {
        _form: "Failed to create service order. Please try again.",
      },
      success: false,
    });
  }
};

export default function CustomerPortalRequestService() {
  const { customer } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const [searchParams] = useSearchParams();
  const deviceId = searchParams.get("deviceId");

  const isSubmitting = navigation.state === "submitting";

  return (
    <div className="space-y-8">
      <div className="bg-gradient-to-r from-primary/10 to-accent/10 rounded-2xl p-8 shadow-lg border border-primary/10">
        <h1 className="text-3xl font-bold mb-2">Request Service</h1>
        <p className="text-muted-foreground">Submit a new service request for your HVAC equipment</p>
      </div>

      <Card className="overflow-hidden">
        <div className="absolute top-0 right-0 left-0 h-1 bg-primary"></div>
        <CardHeader>
          <div className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-primary" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="12" y1="8" x2="12" y2="16"></line>
              <line x1="8" y1="12" x2="16" y2="12"></line>
            </svg>
            <CardTitle>Service Request Form</CardTitle>
          </div>
          <CardDescription>
            Please provide details about the service you need
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form method="post" className="space-y-6">
            {actionData?.errors?._form && (
              <div className="bg-destructive/10 text-destructive p-4 rounded-xl border border-destructive/20 flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 flex-shrink-0" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="8" x2="12" y2="12"></line>
                  <line x1="12" y1="16" x2="12.01" y2="16"></line>
                </svg>
                <span>{actionData.errors._form}</span>
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="title" className="text-sm font-medium flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-primary" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <line x1="4" y1="9" x2="20" y2="9"></line>
                  <line x1="4" y1="15" x2="20" y2="15"></line>
                  <line x1="10" y1="3" x2="8" y2="21"></line>
                  <line x1="16" y1="3" x2="14" y2="21"></line>
                </svg>
                Service Title
              </Label>
              <Input
                id="title"
                name="title"
                placeholder="Brief description of the issue (e.g., AC not cooling, Furnace making noise)"
                required
                className="border-primary/20 focus-visible:ring-primary"
                aria-invalid={actionData?.errors?.title ? true : undefined}
                aria-errormessage={actionData?.errors?.title ? "title-error" : undefined}
              />
              {actionData?.errors?.title && (
                <p className="text-destructive text-sm flex items-center" id="title-error">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="12" y1="8" x2="12" y2="12"></line>
                    <line x1="12" y1="16" x2="12.01" y2="16"></line>
                  </svg>
                  {actionData.errors.title}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="description" className="text-sm font-medium flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-primary" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                  <polyline points="14 2 14 8 20 8"></polyline>
                  <line x1="16" y1="13" x2="8" y2="13"></line>
                  <line x1="16" y1="17" x2="8" y2="17"></line>
                  <polyline points="10 9 9 9 8 9"></polyline>
                </svg>
                Description
              </Label>
              <Textarea
                id="description"
                name="description"
                placeholder="Please describe the issue in detail. Include when it started, any unusual sounds, smells, or behaviors you've noticed."
                rows={4}
                required
                className="border-primary/20 focus-visible:ring-primary resize-none"
                aria-invalid={actionData?.errors?.description ? true : undefined}
                aria-errormessage={actionData?.errors?.description ? "description-error" : undefined}
              />
              {actionData?.errors?.description && (
                <p className="text-destructive text-sm flex items-center" id="description-error">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="12" y1="8" x2="12" y2="12"></line>
                    <line x1="12" y1="16" x2="12.01" y2="16"></line>
                  </svg>
                  {actionData.errors.description}
                </p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="deviceId" className="text-sm font-medium flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-primary" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <rect x="2" y="7" width="20" height="15" rx="2" ry="2"></rect>
                    <polyline points="17 2 12 7 7 2"></polyline>
                  </svg>
                  Device
                </Label>
                <select
                  id="deviceId"
                  name="deviceId"
                  className="flex h-10 w-full rounded-md border border-primary/20 bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  defaultValue={deviceId || ""}
                  required
                  aria-invalid={actionData?.errors?.deviceId ? true : undefined}
                  aria-errormessage={actionData?.errors?.deviceId ? "deviceId-error" : undefined}
                >
                  <option value="" disabled>Select a device</option>
                  {customer.devices.map((device) => (
                    <option key={device.id} value={device.id}>
                      {device.name} ({device.manufacturer} {device.model})
                    </option>
                  ))}
                </select>
                {actionData?.errors?.deviceId && (
                  <p className="text-destructive text-sm flex items-center" id="deviceId-error">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <circle cx="12" cy="12" r="10"></circle>
                      <line x1="12" y1="8" x2="12" y2="12"></line>
                      <line x1="12" y1="16" x2="12.01" y2="16"></line>
                    </svg>
                    {actionData.errors.deviceId}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="priority" className="text-sm font-medium flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-primary" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                  </svg>
                  Priority
                </Label>
                <select
                  id="priority"
                  name="priority"
                  className="flex h-10 w-full rounded-md border border-primary/20 bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  defaultValue="MEDIUM"
                  required
                  aria-invalid={actionData?.errors?.priority ? true : undefined}
                  aria-errormessage={actionData?.errors?.priority ? "priority-error" : undefined}
                >
                  <option value="LOW">Low - Not urgent, can be scheduled anytime</option>
                  <option value="MEDIUM">Medium - Should be addressed soon</option>
                  <option value="HIGH">High - Needs prompt attention</option>
                  <option value="URGENT">Urgent - Emergency situation</option>
                </select>
                {actionData?.errors?.priority && (
                  <p className="text-destructive text-sm flex items-center" id="priority-error">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <circle cx="12" cy="12" r="10"></circle>
                      <line x1="12" y1="8" x2="12" y2="12"></line>
                      <line x1="12" y1="16" x2="12.01" y2="16"></line>
                    </svg>
                    {actionData.errors.priority}
                  </p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="preferredDate" className="text-sm font-medium flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-primary" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                  <line x1="16" y1="2" x2="16" y2="6"></line>
                  <line x1="8" y1="2" x2="8" y2="6"></line>
                  <line x1="3" y1="10" x2="21" y2="10"></line>
                </svg>
                Preferred Service Date (Optional)
              </Label>
              <Input
                id="preferredDate"
                name="preferredDate"
                type="date"
                min={new Date().toISOString().split("T")[0]}
                className="border-primary/20 focus-visible:ring-primary"
              />
              <p className="text-sm text-muted-foreground flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-muted-foreground" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="16" x2="12.01" y2="16"></line>
                  <line x1="12" y1="8" x2="12" y2="12"></line>
                </svg>
                Leave blank if you don't have a preferred date
              </p>
            </div>

            <div className="flex flex-col sm:flex-row justify-between gap-4 pt-4 border-t border-border/30">
              <Button type="button" variant="outline" asChild className="sm:flex-1">
                <Link to="/customer-portal" className="w-full flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <line x1="19" y1="12" x2="5" y2="12"></line>
                    <polyline points="12 19 5 12 12 5"></polyline>
                  </svg>
                  Cancel
                </Link>
              </Button>
              <Button type="submit" disabled={isSubmitting} className="sm:flex-1">
                {isSubmitting ? (
                  <span className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Submitting...
                  </span>
                ) : (
                  <span className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                      <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                    Submit Request
                  </span>
                )}
              </Button>
            </div>
          </Form>
        </CardContent>
      </Card>

      <div className="bg-info/5 rounded-2xl p-6 border border-info/20">
        <div className="flex items-start">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-3 text-info flex-shrink-0" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" y1="16" x2="12.01" y2="16"></line>
            <line x1="12" y1="8" x2="12" y2="12"></line>
          </svg>
          <div>
            <h3 className="text-lg font-medium text-foreground mb-2">What happens next?</h3>
            <p className="text-muted-foreground mb-4">After submitting your service request:</p>
            <ol className="list-decimal list-inside space-y-2 text-sm text-muted-foreground">
              <li>Our team will review your request and assign a technician</li>
              <li>You'll receive a confirmation email with the scheduled date and time</li>
              <li>The technician will contact you before the visit</li>
              <li>After the service is complete, you'll receive a service report</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  );
}