import { json, redirect, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { Form, useActionData, useLoaderData, useNavigation, Link } from "@remix-run/react";
import { useEffect, useRef } from "react";
import { requireUserId } from "~/session.server";
import { getCustomerById, updateCustomer } from "~/services/customer.service";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";

export async function loader({ request, params }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);
  const { customerId } = params;

  if (!customerId) {
    throw new Response("Customer ID is required", { status: 400 });
  }

  const customerResponse = await getCustomerById(customerId, userId);

  if (!customerResponse.success || !customerResponse.data) {
    throw new Response(customerResponse.error || "Customer not found", { status: 404 });
  }

  return json({ customer: customerResponse.data });
}

export async function action({ request, params }: ActionFunctionArgs) {
  const userId = await requireUserId(request);
  const { customerId } = params;

  if (!customerId) {
    throw new Response("Customer ID is required", { status: 400 });
  }

  const formData = await request.formData();

  const name = formData.get("name") as string;
  const email = formData.get("email") as string;
  const phone = formData.get("phone") as string;
  const address = formData.get("address") as string;
  const city = formData.get("city") as string;
  const postalCode = formData.get("postalCode") as string;
  const country = formData.get("country") as string;
  const notes = formData.get("notes") as string;

  // Validate required fields
  const errors = {
    name: name ? null : "Name is required",
  };

  const hasErrors = Object.values(errors).some(
    (errorMessage) => errorMessage !== null
  );

  if (hasErrors) {
    return json({ errors, values: Object.fromEntries(formData) });
  }

  // Update customer
  const customerResponse = await updateCustomer(
    customerId,
    {
      name,
      email: email || null,
      phone: phone || null,
      address: address || null,
      city: city || null,
      postalCode: postalCode || null,
      country: country || null,
      notes: notes || null,
    },
    userId
  );

  if (!customerResponse.success) {
    return json({
      errors: {
        ...errors,
        form: customerResponse.error || "Failed to update customer",
      },
      values: Object.fromEntries(formData),
    });
  }

  return redirect(`/customers/${customerId}`);
}

export default function EditCustomerPage() {
  const { customer } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";

  const nameRef = useRef<HTMLInputElement>(null);

  // Focus on the name field when there's an error
  useEffect(() => {
    if (actionData?.errors?.name) {
      nameRef.current?.focus();
    }
  }, [actionData]);

  return (
    <div className="container py-8">
      <div className="mb-6">
        <Link to={`/customers/${customer.id}`} className="text-blue-500 hover:underline">
          ← Back to Customer Details
        </Link>
      </div>

      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Edit Customer</CardTitle>
          <CardDescription>
            Update customer information
          </CardDescription>
        </CardHeader>

        <Form method="post">
          <CardContent className="space-y-4">
            {/* Form error message */}
            {actionData?.errors?.form && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                {actionData.errors.form}
              </div>
            )}

            {/* Name field */}
            <div className="space-y-2">
              <Label htmlFor="name">
                Name <span className="text-red-500">*</span>
              </Label>
              <Input
                ref={nameRef}
                id="name"
                name="name"
                defaultValue={actionData?.values?.name as string || customer.name}
                aria-invalid={actionData?.errors?.name ? true : undefined}
                aria-errormessage={
                  actionData?.errors?.name ? "name-error" : undefined
                }
              />
              {actionData?.errors?.name && (
                <p className="text-red-500 text-sm" id="name-error">
                  {actionData.errors.name}
                </p>
              )}
            </div>

            {/* Email field */}
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                name="email"
                type="email"
                defaultValue={actionData?.values?.email as string || customer.email || ""}
              />
            </div>

            {/* Phone field */}
            <div className="space-y-2">
              <Label htmlFor="phone">Phone</Label>
              <Input
                id="phone"
                name="phone"
                defaultValue={actionData?.values?.phone as string || customer.phone || ""}
              />
            </div>

            {/* Address field */}
            <div className="space-y-2">
              <Label htmlFor="address">Address</Label>
              <Input
                id="address"
                name="address"
                defaultValue={actionData?.values?.address as string || customer.address || ""}
              />
            </div>

            {/* City field */}
            <div className="space-y-2">
              <Label htmlFor="city">City</Label>
              <Input
                id="city"
                name="city"
                defaultValue={actionData?.values?.city as string || customer.city || ""}
              />
            </div>

            {/* Postal Code field */}
            <div className="space-y-2">
              <Label htmlFor="postalCode">Postal Code</Label>
              <Input
                id="postalCode"
                name="postalCode"
                defaultValue={actionData?.values?.postalCode as string || customer.postalCode || ""}
              />
            </div>

            {/* Country field */}
            <div className="space-y-2">
              <Label htmlFor="country">Country</Label>
              <Input
                id="country"
                name="country"
                defaultValue={actionData?.values?.country as string || customer.country || ""}
              />
            </div>

            {/* Notes field */}
            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                name="notes"
                rows={4}
                defaultValue={actionData?.values?.notes as string || customer.notes || ""}
              />
            </div>
          </CardContent>

          <CardFooter className="flex justify-between">
            <Link to={`/customers/${customer.id}`}>
              <Button type="button" variant="outline">
                Cancel
              </Button>
            </Link>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Saving..." : "Save Changes"}
            </Button>
          </CardFooter>
        </Form>
      </Card>
    </div>
  );
}
