import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { Link, useLoaderData } from "@remix-run/react";
import { ArchiveBoxIcon, MapPinIcon, ArrowUpOnSquareIcon } from "@heroicons/react/24/outline";
import { requireUserId } from "~/session.server";
import { prisma } from "~/db.server";
import type { Prisma } from "@prisma/client";
// import type { JsonifyObject } from "@remix-run/node"; // Removed JsonifyObject import

// Define types based on Prisma schema
// Using Prisma.Type to infer types directly from the schema includes where possible,
// and explicitly defining types for clarity and to resolve potential mismatches.

// Assuming a basic Category type exists in Prisma schema
interface Category {
  id: string;
  name: string;
  // Add other Category fields if needed
}

// Adjusted type to reflect that category might not be directly included if include: true fails
type InventoryPartWithRelations = Prisma.InventoryPartGetPayload<{
  include: {
    inventoryLocations: {
      include: {
        location: true;
      };
    };
    // category: true, // Removing this include based on type error
  };
}> & {
  // Explicitly add the category relation to the type, assuming it might be fetched separately or is optional
  category: Category | null; // Assuming category can be null
};


type InventoryLocationWithParts = Prisma.LocationGetPayload<{
  include: {
    PartLocation: { // Assuming the relation name in Location model is PartLocation
      include: {
        part: true;
      };
    };
  };
}>;

type PartLocationWithRelations = Prisma.PartLocationGetPayload<{
  include: {
    part: true;
    location: true;
  };
}>;

interface Location {
  id: string;
  name: string;
  description?: string | null;
  type: string;
  isActive: boolean;
}

interface Zone {
  id: string;
  name: string;
  x: number;
  y: number;
  width: number;
  height: number;
  color: string;
}

interface Shelf {
  id: string;
  zoneId: string;
  name: string;
  x: number;
  y: number;
  width: number;
  height: number;
}

interface WarehouseLayout {
  width: number;
  height: number;
  zones: Zone[];
  shelves: Shelf[];
}

// ZoneStats interface for data *within the loader* (before JSON serialization)
interface ZoneStatsLoader {
  count: number;
  totalQuantity: number;
  totalValue: number;
  uniqueParts: Set<string>;
}

// ShelfStats interface for data *within the loader* (before JSON serialization)
interface ShelfStatsLoader {
  count: number;
  totalQuantity: number;
  totalValue: number;
  uniqueParts: Set<string>;
}

// ZoneStats interface for data *received by the component* (after JSON serialization)
interface ZoneStatsComponent {
  count: number;
  totalQuantity: number;
  totalValue: number;
  uniquePartsCount: number; // This is the serialized count
}

// ShelfStats interface for data *received by the component* (after JSON serialization)
interface ShelfStatsComponent {
  count: number;
  totalQuantity: number;
  totalValue: number;
  uniquePartsCount: number; // This is the serialized count
}


interface Filters {
  locationId: string;
  view: string;
}

interface LoaderData {
  locations: Location[]; // Use the basic Location interface for the locations list
  parts: InventoryPartWithRelations[]; // Use the type with relations for parts
  warehouseLayout: WarehouseLayout;
  zoneStats: { [key: string]: ZoneStatsComponent }; // Use component type
  shelfStats: { [key: string]: ShelfStatsComponent }; // Use component type
  filters: Filters;
}

export async function loader({ request }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);

  // Get query parameters
  const url = new URL(request.url);
  const locationId = url.searchParams.get("locationId") || "all";
  const view = url.searchParams.get("view") || "heatmap";

  // Get all inventory locations
  const locations = await prisma.location.findMany({
    where: {
      isActive: true,
      type: "INVENTORY"
    },
    orderBy: {
      name: "asc"
    }
  });

  // Get inventory parts with their locations
  const whereClause: Prisma.InventoryPartWhereInput = {
    isActive: true
  };

  if (locationId !== "all") {
    whereClause.inventoryLocations = {
      some: {
        locationId
      }
    };
  }

  const parts = await prisma.inventoryPart.findMany({
    where: whereClause,
    include: {
      inventoryLocations: {
        include: {
          location: true
        }
      },
      // category: true // Removed based on type error
    }
  });

  // Get warehouse layout details if available (placeholder - would come from a separate table in a real implementation)
  const warehouseLayout = {
    width: 1200,
    height: 800,
    // In a real implementation, this would be fetched from a database
    zones: [
      { id: "A", name: "Zone A", x: 50, y: 50, width: 300, height: 300, color: "#4ade80" },
      { id: "B", name: "Zone B", x: 400, y: 50, width: 300, height: 300, color: "#60a5fa" },
      { id: "C", name: "Zone C", x: 750, y: 50, width: 300, height: 300, color: "#f97316" },
      { id: "D", name: "Zone D", x: 50, y: 400, width: 300, height: 300, color: "#facc15" },
      { id: "E", name: "Zone E", x: 400, y: 400, width: 300, height: 300, color: "#c084fc" },
      { id: "F", name: "Zone F", x: 750, y: 400, width: 300, height: 300, color: "#f43f5e" }
    ],
    shelves: [
      // Zone A shelves
      { id: "A1", zoneId: "A", name: "A1", x: 80, y: 80, width: 240, height: 40 },
      { id: "A2", zoneId: "A", name: "A2", x: 80, y: 140, width: 240, height: 40 },
      { id: "A3", zoneId: "A", name: "A3", x: 80, y: 200, width: 240, height: 40 },
      { id: "A4", zoneId: "A", name: "A4", x: 80, y: 260, width: 240, height: 40 },

      // Zone B shelves
      { id: "B1", zoneId: "B", name: "B1", x: 430, y: 80, width: 240, height: 40 },
      { id: "B2", zoneId: "B", name: "B2", x: 430, y: 140, width: 240, height: 40 },
      { id: "B3", zoneId: "B", name: "B3", x: 430, y: 200, width: 240, height: 40 },
      { id: "B4", zoneId: "B", name: "B4", x: 430, y: 260, width: 240, height: 40 },

      // Zone C shelves
      { id: "C1", zoneId: "C", name: "C1", x: 780, y: 80, width: 240, height: 40 },
      { id: "C2", zoneId: "C", name: "C2", x: 780, y: 140, width: 240, height: 40 },
      { id: "C3", zoneId: "C", name: "C3", x: 780, y: 200, width: 240, height: 40 },
      { id: "C4", zoneId: "C", name: "C4", x: 780, y: 260, width: 240, height: 40 },

      // Zone D shelves
      { id: "D1", zoneId: "D", name: "D1", x: 80, y: 430, width: 240, height: 40 },
      { id: "D2", zoneId: "D", name: "D2", x: 80, y: 490, width: 240, height: 40 },
      { id: "D3", zoneId: "D", name: "D3", x: 80, y: 550, width: 240, height: 40 },
      { id: "D4", zoneId: "D", name: "D4", x: 80, y: 610, width: 240, height: 40 },

      // Zone E shelves
      { id: "E1", zoneId: "E", name: "E1", x: 430, y: 430, width: 240, height: 40 },
      { id: "E2", zoneId: "E", name: "E2", x: 430, y: 490, width: 240, height: 40 },
      { id: "E3", zoneId: "E", name: "E3", x: 430, y: 550, width: 240, height: 40 },
      { id: "E4", zoneId: "E", name: "E4", x: 430, y: 610, width: 240, height: 40 },

      // Zone F shelves
      { id: "F1", zoneId: "F", name: "F1", x: 780, y: 430, width: 240, height: 40 },
      { id: "F2", zoneId: "F", name: "F2", x: 780, y: 490, width: 240, height: 40 },
      { id: "F3", zoneId: "F", name: "F3", x: 780, y: 550, width: 240, height: 40 },
      { id: "F4", zoneId: "F", name: "F4", x: 780, y: 610, width: 240, height: 40 },
    ]
  };

  // Map location names to warehouse zones/shelves (placeholder - would be from database normally)
  const locationMapping = {
    // For each location, assign a zone and optionally a specific shelf
    // This would come from the database in a real implementation
  };

  // Assign parts to zones/shelves for visualization
  const partsWithLocations = parts.map(part => {
    // Get the first location for simplicity (in reality, would use the locationMapping)
    const firstLocation = part.inventoryLocations[0]?.location;

    // Randomly assign to a zone/shelf for demo purposes
    // In a real implementation, this would use actual location data from the database
    const zoneId = String.fromCharCode(65 + Math.floor(Math.random() * 6)); // A-F
    const shelfNum = Math.floor(Math.random() * 4) + 1; // 1-4
    const shelfId = `${zoneId}${shelfNum}`;

    const zone = warehouseLayout.zones.find(z => z.id === zoneId);
    const shelf = warehouseLayout.shelves.find(s => s.id === shelfId);

    return {
      ...part,
      location: firstLocation?.name || "Unassigned",
      locationId: firstLocation?.id,
      zoneId,
      zone,
      shelfId,
      shelf,
      quantity: part.inventoryLocations[0]?.quantity || 0,
      // Assuming category is not included in the query result based on type error
      category: null, // Explicitly set category to null or fetch separately if needed
    };
  });

  // Count items per zone and shelf for heatmap
  const zoneStats: { [key: string]: ZoneStatsLoader } = {}; // Use loader type
  const shelfStats: { [key: string]: ShelfStatsLoader } = {}; // Use loader type

  partsWithLocations.forEach(part => {
    // Aggregate by zone
    if (!zoneStats[part.zoneId]) {
      zoneStats[part.zoneId] = {
        count: 0,
        totalQuantity: 0,
        totalValue: 0,
        uniqueParts: new Set()
      };
    }
    zoneStats[part.zoneId].count += 1;
    zoneStats[part.zoneId].totalQuantity += part.quantity;
    zoneStats[part.zoneId].totalValue += (part.costPrice || 0) * part.quantity;
    zoneStats[part.zoneId].uniqueParts.add(part.id);

    // Aggregate by shelf
    if (!shelfStats[part.shelfId]) {
      shelfStats[part.shelfId] = {
        count: 0,
        totalQuantity: 0,
        totalValue: 0,
        uniqueParts: new Set()
      };
    }
    shelfStats[part.shelfId].count += 1;
    shelfStats[part.shelfId].totalQuantity += part.quantity;
    shelfStats[part.shelfId].totalValue += (part.costPrice || 0) * part.quantity;
    shelfStats[part.shelfId].uniqueParts.add(part.id);
  });

  // Convert Sets to counts for JSON serialization
  const serializedZoneStats: { [key: string]: ZoneStatsComponent } = {}; // Use component type for serialization
  Object.keys(zoneStats).forEach(key => {
    serializedZoneStats[key] = {
      count: zoneStats[key].count,
      totalQuantity: zoneStats[key].totalQuantity,
      totalValue: zoneStats[key].totalValue,
      uniquePartsCount: zoneStats[key].uniqueParts.size,
    };
  });

  const serializedShelfStats: { [key: string]: ShelfStatsComponent } = {}; // Use component type for serialization
  Object.keys(shelfStats).forEach(key => {
    serializedShelfStats[key] = {
      count: shelfStats[key].count,
      totalQuantity: shelfStats[key].totalQuantity,
      totalValue: shelfStats[key].totalValue,
      uniquePartsCount: shelfStats[key].uniqueParts.size,
    };
  });

  return json({
    locations,
    parts: partsWithLocations,
    warehouseLayout,
    zoneStats: serializedZoneStats, // Return serialized stats
    shelfStats: serializedShelfStats, // Return serialized stats
    filters: {
      locationId,
      view
    }
  });
}

export default function InventoryVisualMap() {
  const {
    locations,
    parts,
    warehouseLayout,
    zoneStats, // This will be JsonifyObject<{[key: string]: ZoneStatsComponent}>
    shelfStats, // This will be JsonifyObject<{[key: string]: ShelfStatsComponent}>
    filters
  } = useLoaderData<typeof loader>();

  // Calculate zone heat colors based on stats
  const calculateHeatColor = (value: number, max: number, baseColor: [number, number, number] = [76, 175, 80]): string => {
    // Convert value to intensity (0-1)
    const intensity = Math.min(value / max, 1);

    // Blend with white based on intensity
    const r = Math.round(255 - ((255 - baseColor[0]) * intensity));
    const g = Math.round(255 - ((255 - baseColor[1]) * intensity));
    const b = Math.round(255 - ((255 - baseColor[2]) * intensity));

    return `rgb(${r}, ${g}, ${b})`;
  };

  // Find max values for normalization
  // Use type assertion to treat JsonifyObject as the component type for mapping
  const maxZoneCount = Math.max(...Object.values(zoneStats as {[key: string]: ZoneStatsComponent}).map((z: ZoneStatsComponent) => z.count), 1);
  const maxZoneQuantity = Math.max(...Object.values(zoneStats as {[key: string]: ZoneStatsComponent}).map((z: ZoneStatsComponent) => z.totalQuantity), 1);
  const maxZoneValue = Math.max(...Object.values(zoneStats as {[key: string]: ZoneStatsComponent}).map((z: ZoneStatsComponent) => z.totalValue), 1);

  const maxShelfCount = Math.max(...Object.values(shelfStats as {[key: string]: ShelfStatsComponent}).map((s: ShelfStatsComponent) => s.count), 1);
  const maxShelfQuantity = Math.max(...Object.values(shelfStats as {[key: string]: ShelfStatsComponent}).map((s: ShelfStatsComponent) => s.totalQuantity), 1);
  const maxShelfValue = Math.max(...Object.values(shelfStats as {[key: string]: ShelfStatsComponent}).map((s: ShelfStatsComponent) => s.totalValue), 1);

  // Get heat value based on current view
  const getZoneHeatValue = (zoneId: string): number => {
    const stats = zoneStats[zoneId];
    if (!stats) return 0;

    switch (filters.view) {
      case "count":
        return stats.uniquePartsCount / maxZoneCount;
      case "quantity":
        return stats.totalQuantity / maxZoneQuantity;
      case "value":
        return stats.totalValue / maxZoneValue;
      default:
        return stats.uniquePartsCount / maxZoneCount;
    }
  };

  const getShelfHeatValue = (shelfId: string): number => {
    const stats = shelfStats[shelfId];
    if (!stats) return 0;

    switch (filters.view) {
      case "count":
        return stats.uniquePartsCount / maxShelfCount;
      case "quantity":
        return stats.totalQuantity / maxShelfQuantity;
      case "value":
        return stats.totalValue / maxShelfValue;
      default:
        return stats.uniquePartsCount / maxShelfCount;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Visual Inventory Map</h1>
        <div className="flex gap-2">
          <Link
            to="/inventory"
            className="flex items-center gap-1 rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50"
          >
            <ArchiveBoxIcon className="h-4 w-4" />
            Inventory List
          </Link>
          <button
            className="flex items-center gap-1 rounded-md bg-blue-600 px-3 py-2 text-sm font-medium text-white hover:bg-blue-700"
          >
            <ArrowUpOnSquareIcon className="h-4 w-4" />
            Export Map
          </button>
        </div>
      </div>

      {/* Map Controls */}
      <div className="rounded-md border border-gray-200 bg-white p-4 shadow">
        <div className="flex flex-wrap gap-4">
          <div>
            <label htmlFor="locationId" className="block text-sm font-medium text-gray-700">Location</label>
            <select
              id="locationId"
              name="locationId"
              className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
              defaultValue={filters.locationId}
              onChange={(e) => {
                const url = new URL(window.location.href);
                url.searchParams.set("locationId", e.target.value);
                window.location.href = url.toString();
              }}
            >
              <option value="all">All Locations</option>
              {locations.map((location: Location) => ( // Explicitly type location
                <option key={location.id} value={location.id}>{location.name}</option>
              ))}
            </select>
          </div>

          <div>
            <label htmlFor="view" className="block text-sm font-medium text-gray-700">Heat Map View</label>
            <select
              id="view"
              name="view"
              className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
              defaultValue={filters.view}
              onChange={(e) => {
                const url = new URL(window.location.href);
                url.searchParams.set("view", e.target.value);
                window.location.href = url.toString();
              }}
            >
              <option value="count">Part Count</option>
              <option value="quantity">Total Quantity</option>
              <option value="value">Total Value</option>
            </select>
          </div>

          <div className="ml-auto flex items-end space-x-2">
            <div className="flex items-center">
              <span className="mr-2 inline-block h-4 w-8 bg-gradient-to-r from-white to-green-500"></span>
              <span className="text-xs text-gray-500">
                {filters.view === "count" ? "Part Count" :
                filters.view === "quantity" ? "Total Quantity" : "Total Value"}
              </span>
            </div>
            <button
              className="inline-flex items-center rounded border border-gray-300 bg-white px-2.5 py-1.5 text-xs font-medium text-gray-700 shadow-sm hover:bg-gray-50"
            >
              <MapPinIcon className="-ml-0.5 mr-1 h-4 w-4" aria-hidden="true" />
              Reset View
            </button>
          </div>
        </div>
      </div>

      {/* Warehouse Visualization */}
      <div className="rounded-lg border border-gray-200 bg-white p-4 shadow">
        <div className="flex h-[800px] w-full items-center justify-center overflow-auto bg-gray-50">
          <svg width={warehouseLayout.width} height={warehouseLayout.height} viewBox={`0 0 ${warehouseLayout.width} ${warehouseLayout.height}`} className="border border-gray-200 bg-white">
            {/* Background Grid */}
            <pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse">
              <path d="M 50 0 L 0 0 0 50" fill="none" stroke="rgba(0, 0, 0, 0.05)" strokeWidth="1"/>
            </pattern>
            <rect width="100%" height="100%" fill="url(#grid)" />

            {/* Draw Zones */}
            {warehouseLayout.zones.map(zone => {
              // Get heat intensity based on current view
              const zoneIntensity = getZoneHeatValue(zone.id);
              const fillColor = filters.view === "heatmap"
                ? zone.color
                : calculateHeatColor(zoneIntensity * 100, 100, [46, 163, 42]);

              return (
                <g key={zone.id}>
                  <rect
                    x={zone.x}
                    y={zone.y}
                    width={zone.width}
                    height={zone.height}
                    fill={fillColor}
                    fillOpacity={0.3 + (zoneIntensity * 0.7)}
                    stroke={zone.color}
                    strokeWidth="2"
                  />
                  <text
                    x={zone.x + zone.width / 2}
                    y={zone.y + 20}
                    textAnchor="middle"
                    fill="#1f2937"
                    fontWeight="bold"
                  >
                    {zone.name}
                  </text>

                  {/* Show zone stats if available */}
                  {zoneStats[zone.id] && (
                    <text
                      x={zone.x + zone.width / 2}
                      y={zone.y + 40}
                      textAnchor="middle"
                      fill="#1f2937"
                      fontSize="12"
                    >
                      {filters.view === "count" && `${zoneStats[zone.id].uniquePartsCount} parts`}
                      {filters.view === "quantity" && `${zoneStats[zone.id].totalQuantity} units`}
                      {filters.view === "value" && `${new Intl.NumberFormat('pl-PL', { style: 'currency', currency: 'PLN' }).format(zoneStats[zone.id].totalValue)}`}
                    </text>
                  )}
                </g>
              );
            })}

            {/* Draw Shelves */}
            {warehouseLayout.shelves.map(shelf => {
              // Get heat intensity based on current view
              const shelfIntensity = getShelfHeatValue(shelf.id);

              return (
                <g key={shelf.id}>
                  <rect
                    x={shelf.x}
                    y={shelf.y}
                    width={shelf.width}
                    height={shelf.height}
                    fill="#ffffff"
                    stroke="#000000"
                    strokeWidth="1"
                    fillOpacity={0.7 + (shelfIntensity * 0.3)}
                  />
                  <text
                    x={shelf.x + 20}
                    y={shelf.y + shelf.height / 2 + 5}
                    textAnchor="start"
                    fill="#1f2937"
                    fontWeight="medium"
                    fontSize="12"
                  >
                    {shelf.name}
                  </text>

                  {/* Show shelf stats if available and significant */}
                  {shelfStats[shelf.id] && shelfStats[shelf.id].count > 0 && (
                    <text
                      x={shelf.x + shelf.width - 10}
                      y={shelf.y + shelf.height / 2 + 5}
                      textAnchor="end"
                      fill="#1f2937"
                      fontSize="10"
                    >
                      {filters.view === "count" && `${shelfStats[shelf.id].uniquePartsCount}`}
                      {filters.view === "quantity" && `${shelfStats[shelf.id].totalQuantity}`}
                      {filters.view === "value" && `${new Intl.NumberFormat('pl-PL', { maximumFractionDigits: 0, style: 'currency', currency: 'PLN' }).format(shelfStats[shelf.id].totalValue)}`}
                    </text>
                  )}
                </g>
              );
            })}

            {/* Add Legend */}
            <g transform="translate(20, 20)">
              <rect x="0" y="0" width="200" height="80" fill="white" fillOpacity="0.8" stroke="#e5e7eb" rx="4" />
              <text x="10" y="20" fontWeight="bold" fontSize="12">Legend</text>

              <rect x="10" y="30" width="15" height="15" fill="#4ade80" fillOpacity="0.3" stroke="#4ade80" />
              <text x="35" y="42" fontSize="12">Zone</text>

              <rect x="10" y="55" width="15" height="8" fill="#ffffff" stroke="#000000" strokeWidth="1" />
              <text x="35" y="62" fontSize="12">Shelf</text>

              <rect x="100" y="30" width="80" height="10" fill="url(#intensity-gradient)" />
              <text x="100" y="55" fontSize="10" textAnchor="start">Low</text>
              <text x="180" y="55" fontSize="10" textAnchor="end">High</text>
            </g>

            {/* Heat Map Gradient */}
            <defs>
              <linearGradient id="intensity-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#ffffff" />
                <stop offset="100%" stopColor="#22c55e" />
              </linearGradient>
            </defs>
          </svg>
        </div>
      </div>

      {/* Parts List by Zone */}
      <div className="rounded-lg border border-gray-200 bg-white p-6 shadow">
        <h2 className="text-xl font-medium text-gray-900">Parts by Zone</h2>

        <div className="mt-6 space-y-6">
          {warehouseLayout.zones.map(zone => {
            const zoneParts = parts.filter(p => p.zoneId === zone.id);

            if (zoneParts.length === 0) return null;

            return (
              <div key={zone.id} className="rounded-md border border-gray-200">
                <div className="border-b border-gray-200 bg-gray-50 px-4 py-3">
                  <h3 className="text-lg font-medium text-gray-900">
                    {zone.name} - {zoneParts.length} parts
                  </h3>
                </div>

                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Part</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Shelf</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Category</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Quantity</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Value</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 bg-white">
                      {zoneParts.map(part => (
                        <tr key={part.id}>
                          <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                            <Link to={`/inventory/parts/${part.id}`} className="text-blue-600 hover:underline">
                              {part.name}
                            </Link>
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">{part.shelf?.name || "Unassigned"}</td>
                          <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">{part.category?.name || "Uncategorized"}</td>
                          <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">{part.quantity}</td>
                          <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            {new Intl.NumberFormat('pl-PL', { style: 'currency', currency: 'PLN' }).format((part.costPrice || 0) * part.quantity)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Quick Statistics */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
        <div className="rounded-lg border border-gray-200 bg-white p-6 shadow">
          <h3 className="text-lg font-medium text-gray-900">Zone Efficiency</h3>
          <div className="mt-4 space-y-4">
            {Object.entries(zoneStats)
              .sort((a, b) => b[1].uniquePartsCount - a[1].uniquePartsCount)
              .map(([zoneId, stats]: [string, ZoneStatsComponent]) => { // Use component type
                const zone = warehouseLayout.zones.find(z => z.id === zoneId);
                if (!zone) return null; // Add null check

                return (
                  <div key={zoneId} className="flex items-center">
                    <span className="mr-2 inline-block h-3 w-3" style={{ backgroundColor: zone.color }}></span>
                    <span className="min-w-24 text-sm font-medium text-gray-900">{zone.name}</span>
                    <div className="ml-2 flex-1">
                      <div className="flex h-2 overflow-hidden rounded bg-gray-200">
                        <div
                          className="flex flex-col justify-center overflow-hidden bg-blue-500 text-xs text-white text-center whitespace-nowrap"
                          style={{ width: `${(stats.uniquePartsCount / maxZoneCount) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                    <span className="ml-2 text-sm text-gray-500">{stats.uniquePartsCount} parts</span>
                  </div>
                );
              })
            }
          </div>
        </div>

        <div className="rounded-lg border border-gray-200 bg-white p-6 shadow">
          <h3 className="text-lg font-medium text-gray-900">Zone Quantity Distribution</h3>
          <div className="mt-4 space-y-4">
            {Object.entries(zoneStats)
              .sort((a, b) => b[1].totalQuantity - a[1].totalQuantity)
              .map(([zoneId, stats]: [string, ZoneStatsComponent]) => { // Use component type
                const zone = warehouseLayout.zones.find(z => z.id === zoneId);
                if (!zone) return null; // Add null check

                return (
                  <div key={zoneId} className="flex items-center">
                    <span className="mr-2 inline-block h-3 w-3" style={{ backgroundColor: zone.color }}></span>
                    <span className="min-w-24 text-sm font-medium text-gray-900">{zone.name}</span>
                    <div className="ml-2 flex-1">
                      <div className="flex h-2 overflow-hidden rounded bg-gray-200">
                        <div
                          className="flex flex-col justify-center overflow-hidden bg-green-500 text-xs text-white text-center whitespace-nowrap"
                          style={{ width: `${(stats.totalQuantity / maxZoneQuantity) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                    <span className="ml-2 text-sm text-gray-500">{stats.totalQuantity} units</span>
                  </div>
                );
              })
            }
          </div>
        </div>

        <div className="rounded-lg border border-gray-200 bg-white p-6 shadow">
          <h3 className="text-lg font-medium text-gray-900">Zone Value Distribution</h3>
          <div className="mt-4 space-y-4">
            {Object.entries(zoneStats)
              .sort((a, b) => b[1].totalValue - a[1].totalValue)
              .map(([zoneId, stats]: [string, ZoneStatsComponent]) => { // Use component type
                const zone = warehouseLayout.zones.find(z => z.id === zoneId);
                if (!zone) return null; // Add null check

                return (
                  <div key={zoneId} className="flex items-center">
                    <span className="mr-2 inline-block h-3 w-3" style={{ backgroundColor: zone.color }}></span>
                    <span className="min-w-24 text-sm font-medium text-gray-900">{zone.name}</span>
                    <div className="ml-2 flex-1">
                      <div className="flex h-2 overflow-hidden rounded bg-gray-200">
                        <div
                          className="flex flex-col justify-center overflow-hidden bg-yellow-500 text-xs text-white text-center whitespace-nowrap"
                          style={{ width: `${(stats.totalValue / maxZoneValue) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                    <span className="ml-2 text-sm text-gray-500">
                      {new Intl.NumberFormat('pl-PL', { style: 'currency', currency: 'PLN', maximumFractionDigits: 0 }).format(stats.totalValue)}
                    </span>
                  </div>
                );
              })
            }
          </div>
        </div>
      </div>

      {/* Recommendations */}
      <div className="rounded-lg border border-gray-200 bg-white p-6 shadow">
        <h2 className="text-xl font-medium text-gray-900">Inventory Map Recommendations</h2>

        <div className="mt-4 space-y-4">
          <div className="rounded-md bg-blue-50 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-blue-800">Zone Optimization</h3>
                <div className="mt-2 text-sm text-blue-700">
                  <p>Based on movement patterns, consider reorganizing Zone {Object.entries(zoneStats).sort((a, b) => b[1].totalQuantity - a[1].totalQuantity)[0][0]}.</p>
                </div>
              </div>
            </div>
          </div>

          <div className="rounded-md bg-green-50 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-green-800">Value Distribution</h3>
                <div className="mt-2 text-sm text-green-700">
                  <p>High-value items are concentrated in Zone {Object.entries(zoneStats).sort((a, b) => b[1].totalValue - a[1].totalValue)[0][0]}. Consider implementing additional security measures for this zone.</p>
                </div>
              </div>
            </div>
          </div>

          <div className="rounded-md bg-yellow-50 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-yellow-800">Space Utilization</h3>
                <div className="mt-2 text-sm text-yellow-700">
                  <p>Zone {Object.entries(zoneStats).sort((a, b) => a[1].uniquePartsCount - b[1].uniquePartsCount)[0][0]} is underutilized. Consider relocating some items to this zone to balance distribution.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
