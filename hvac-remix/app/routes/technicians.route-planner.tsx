import { json, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { Form, Link, useLoaderData, useActionData, useNavigation } from "@remix-run/react";
import { useState } from "react";
import { requireUserId } from "~/session.server";
import { optimizeRoutes, getRouteOptimizationSuggestions } from "~/services/route-optimization.server";
import { prisma } from "~/db.server";
import {
  MapPinIcon,
  ClockIcon,
  TruckIcon,
  ArrowPathIcon,
  CalendarIcon,
  UserGroupIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ArrowRightIcon
} from "@heroicons/react/24/outline";

export async function loader({ request }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);

  // Get date from URL params or default to today
  const url = new URL(request.url);
  const dateParam = url.searchParams.get("date");
  const selectedDate = dateParam ? new Date(dateParam) : new Date();

  // Get available technicians
  const technicians = await prisma.user.findMany({
    where: {
      role: 'TECHNICIAN',
    },
    select: {
      id: true,
      name: true,
      email: true,
    },
    orderBy: {
      name: 'asc',
    },
  });

  // Get optimization suggestions
  const suggestions = await getRouteOptimizationSuggestions(userId, selectedDate);

  return json({
    technicians,
    suggestions,
    selectedDate: selectedDate.toISOString().split('T')[0],
  });
}

export async function action({ request }: ActionFunctionArgs) {
  const userId = await requireUserId(request);
  const formData = await request.formData();
  const action = formData.get("action");

  if (action === "optimizeRoutes") {
    const date = new Date(formData.get("date") as string);
    const technicianIds = formData.getAll("technicianIds") as string[];
    const maxStopsPerRoute = Number(formData.get("maxStopsPerRoute")) || 8;
    const considerTraffic = formData.get("considerTraffic") === "true";
    const prioritizeUrgent = formData.get("prioritizeUrgent") === "true";

    try {
      const optimizedRoutes = await optimizeRoutes(userId, {
        date,
        technicianIds: technicianIds.length > 0 ? technicianIds : undefined,
        maxStopsPerRoute,
        considerTraffic,
        prioritizeUrgent,
        respectTimeWindows: true,
      });

      return json({
        success: true,
        routes: optimizedRoutes,
        message: `Generated ${optimizedRoutes.length} optimized routes`,
      });
    } catch (error: any) {
      return json({
        success: false,
        error: error.message,
      }, { status: 400 });
    }
  }

  return json({ success: false, error: "Invalid action" }, { status: 400 });
}

export default function RoutePlanner() {
  const { technicians, suggestions, selectedDate } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const [selectedTechnicians, setSelectedTechnicians] = useState<string[]>([]);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);

  const isOptimizing = navigation.state === "submitting";

  const handleTechnicianToggle = (technicianId: string) => {
    setSelectedTechnicians(prev =>
      prev.includes(technicianId)
        ? prev.filter(id => id !== technicianId)
        : [...prev, technicianId]
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Route Planner</h1>
          <p className="mt-2 text-gray-600">
            Optimize technician routes for maximum efficiency
          </p>
        </div>
        <Link
          to="/technicians"
          className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
        >
          <UserGroupIcon className="h-4 w-4 mr-2" />
          Manage Technicians
        </Link>
      </div>

      {/* Optimization Suggestions */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center mb-3">
          <ExclamationTriangleIcon className="h-5 w-5 text-blue-400 mr-2" />
          <h3 className="text-sm font-medium text-blue-800">
            Route Optimization Insights
          </h3>
        </div>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{suggestions.totalStops}</div>
            <div className="text-sm text-blue-700">Total Stops</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">{suggestions.unassignedStops}</div>
            <div className="text-sm text-blue-700">Unassigned</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {suggestions.potentialSavings.timeMinutes}min
            </div>
            <div className="text-sm text-blue-700">Potential Savings</div>
          </div>
        </div>
        {suggestions.suggestions.length > 0 && (
          <div className="mt-4">
            <h4 className="text-sm font-medium text-blue-800 mb-2">Suggestions:</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              {suggestions.suggestions.map((suggestion, index) => (
                <li key={index} className="flex items-start">
                  <span className="mr-2">•</span>
                  <span>{suggestion}</span>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>

      {/* Route Optimization Form */}
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Generate Optimized Routes</h2>

        <Form method="post" className="space-y-6">
          <input type="hidden" name="action" value="optimizeRoutes" />

          {/* Date Selection */}
          <div>
            <label htmlFor="date" className="block text-sm font-medium text-gray-700 mb-2">
              Date
            </label>
            <input
              type="date"
              id="date"
              name="date"
              defaultValue={selectedDate}
              className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
          </div>

          {/* Technician Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Technicians
            </label>
            <div className="grid grid-cols-1 gap-2 sm:grid-cols-2 lg:grid-cols-3">
              {technicians.map((technician) => (
                <label key={technician.id} className="flex items-center">
                  <input
                    type="checkbox"
                    name="technicianIds"
                    value={technician.id}
                    checked={selectedTechnicians.includes(technician.id)}
                    onChange={() => handleTechnicianToggle(technician.id)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">{technician.name}</span>
                </label>
              ))}
            </div>
            {selectedTechnicians.length === 0 && (
              <p className="mt-2 text-sm text-gray-500">
                Leave empty to include all available technicians
              </p>
            )}
          </div>

          {/* Advanced Options */}
          <div>
            <button
              type="button"
              onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
              className="flex items-center text-sm font-medium text-blue-600 hover:text-blue-500"
            >
              Advanced Options
              <ArrowRightIcon className={`ml-1 h-4 w-4 transform transition-transform ${
                showAdvancedOptions ? 'rotate-90' : ''
              }`} />
            </button>

            {showAdvancedOptions && (
              <div className="mt-4 space-y-4 p-4 bg-gray-50 rounded-md">
                <div>
                  <label htmlFor="maxStopsPerRoute" className="block text-sm font-medium text-gray-700 mb-2">
                    Maximum Stops per Route
                  </label>
                  <input
                    type="number"
                    id="maxStopsPerRoute"
                    name="maxStopsPerRoute"
                    defaultValue={8}
                    min={1}
                    max={20}
                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>

                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      name="considerTraffic"
                      value="true"
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="ml-2 text-sm text-gray-700">Consider traffic patterns</span>
                  </label>

                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      name="prioritizeUrgent"
                      value="true"
                      defaultChecked
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="ml-2 text-sm text-gray-700">Prioritize urgent service orders</span>
                  </label>
                </div>
              </div>
            )}
          </div>

          {/* Submit Button */}
          <div className="flex justify-end">
            <button
              type="submit"
              disabled={isOptimizing}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
            >
              <ArrowPathIcon className={`h-4 w-4 mr-2 ${isOptimizing ? 'animate-spin' : ''}`} />
              {isOptimizing ? "Optimizing..." : "Optimize Routes"}
            </button>
          </div>
        </Form>
      </div>

      {/* Optimized Routes Results */}
      {actionData?.success && actionData.routes && (
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-medium text-gray-900">Optimized Routes</h2>
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              <CheckCircleIcon className="h-3 w-3 mr-1" />
              {actionData.routes.length} routes generated
            </span>
          </div>

          <div className="space-y-6">
            {actionData.routes.map((route: any, index: number) => (
              <div key={route.technicianId} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium ${
                      index % 4 === 0 ? 'bg-blue-500' :
                      index % 4 === 1 ? 'bg-green-500' :
                      index % 4 === 2 ? 'bg-purple-500' : 'bg-orange-500'
                    }`}>
                      {index + 1}
                    </div>
                    <div className="ml-3">
                      <h3 className="text-lg font-medium text-gray-900">{route.technicianName}</h3>
                      <p className="text-sm text-gray-500">{route.stops.length} stops</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-gray-500">Efficiency</div>
                    <div className="text-lg font-semibold text-green-600">{route.efficiency}%</div>
                  </div>
                </div>

                {/* Route Metrics */}
                <div className="grid grid-cols-3 gap-4 mb-4 p-3 bg-gray-50 rounded-md">
                  <div className="text-center">
                    <div className="text-sm text-gray-500">Distance</div>
                    <div className="font-medium">{route.totalDistance} km</div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm text-gray-500">Service Time</div>
                    <div className="font-medium">{Math.round(route.totalDuration / 60)}h {route.totalDuration % 60}m</div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm text-gray-500">Travel Time</div>
                    <div className="font-medium">{Math.round(route.totalTravelTime / 60)}h {route.totalTravelTime % 60}m</div>
                  </div>
                </div>

                {/* Route Stops */}
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-gray-700">Route Stops:</h4>
                  {route.stops.map((stop: any, stopIndex: number) => (
                    <div key={stop.id} className="flex items-center p-2 bg-white border border-gray-100 rounded">
                      <div className="flex-shrink-0 w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center text-xs font-medium">
                        {stopIndex + 1}
                      </div>
                      <div className="ml-3 flex-1">
                        <div className="text-sm font-medium text-gray-900">{stop.customerName}</div>
                        <div className="text-xs text-gray-500">{stop.address}</div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          stop.priority === 'URGENT' ? 'bg-red-100 text-red-800' :
                          stop.priority === 'HIGH' ? 'bg-orange-100 text-orange-800' :
                          stop.priority === 'MEDIUM' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {stop.priority}
                        </span>
                        <span className="text-xs text-gray-500">{stop.estimatedDuration}min</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Error Message */}
      {actionData?.success === false && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-400 mr-2" />
            <span className="text-sm text-red-800">{actionData.error}</span>
          </div>
        </div>
      )}
    </div>
  );
}
